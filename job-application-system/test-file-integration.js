// Test script for file integration functionality
import { scanDownloadsDirectory, validateFile, determineAttachmentType, createAttachmentsFromFiles } from './src/utils/fileManager.js';
import { integrateFilesIntoApplicantData } from './src/services/fileIntegrationService.js';

// Mock applicant data for testing
const testApplicantData = [
  {
    id: 1,
    uid: "123456",
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "CLOUD INFRASTRUCTURE ENGINEER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "BA / BSc / HND",
    applicationDate: "2024-01-15",
    status: "Under Review",
    attachments: []
  }
];

async function testFileIntegration() {
  console.log('🧪 Testing File Integration System');
  console.log('=====================================\n');

  try {
    // Test 1: Scan downloads directory
    console.log('📁 Test 1: Scanning downloads directory...');
    const files = await scanDownloadsDirectory();
    console.log(`Found ${files.length} valid files:`);
    files.forEach(file => {
      console.log(`  - ${file.name} (${file.type}, ${file.size} bytes)`);
    });
    console.log('✅ Downloads directory scan completed\n');

    // Test 2: File validation
    console.log('🔍 Test 2: Testing file validation...');
    const testFiles = [
      { name: 'CV1.docx', size: 150000 },
      { name: 'cert1.jpg', size: 180000 },
      { name: 'invalid.txt', size: 50000 },
      { name: 'toolarge.pdf', size: 15000000 }
    ];

    testFiles.forEach(file => {
      const validation = validateFile(file.name, file.size);
      console.log(`  ${file.name}: ${validation.isValid ? '✅ Valid' : '❌ Invalid'} ${validation.error || ''}`);
    });
    console.log('✅ File validation tests completed\n');

    // Test 3: Attachment type determination
    console.log('🏷️  Test 3: Testing attachment type determination...');
    const testFileNames = ['CV1.docx', 'cert1.jpg', 'testimonial1.docx', 'cover_letter.pdf'];
    testFileNames.forEach(fileName => {
      const type = determineAttachmentType(fileName);
      console.log(`  ${fileName} → ${type}`);
    });
    console.log('✅ Attachment type determination tests completed\n');

    // Test 4: Create attachments from files
    console.log('📎 Test 4: Creating attachments from files...');
    const attachments = createAttachmentsFromFiles(files);
    console.log(`Created ${attachments.length} attachment objects:`);
    attachments.forEach(att => {
      console.log(`  - ${att.name} (${att.type}) - ${att.label}`);
    });
    console.log('✅ Attachment creation completed\n');

    // Test 5: Full integration test
    console.log('🔄 Test 5: Full integration test...');
    const result = await integrateFilesIntoApplicantData(testApplicantData);
    
    if (result.success) {
      console.log('✅ Integration successful!');
      console.log(`📊 Statistics:`, result.stats);
      console.log(`📝 Message: ${result.message}`);
      
      console.log('\n📋 Updated applicant attachments:');
      result.data[0].attachments.forEach(att => {
        console.log(`  - ${att.name} (${att.type}) - ${att.size} - ${att.label}`);
      });
    } else {
      console.log('❌ Integration failed:', result.message);
    }
    console.log('✅ Full integration test completed\n');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests
testFileIntegration();
