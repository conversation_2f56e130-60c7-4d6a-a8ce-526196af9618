<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Download Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        .file-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Download Functionality Test</h1>
    
    <div class="test-section">
        <h2>Available Files for Download</h2>
        <div id="file-list"></div>
    </div>

    <script>
        const testFiles = [
            { name: 'CV1.docx', type: 'cv' },
            { name: 'CV2.pdf', type: 'cv' },
            { name: 'cert1.jpg', type: 'certificate' },
            { name: 'cert2.png', type: 'certificate' },
            { name: 'testimonial1.docx', type: 'testimonial' }
        ];

        function downloadFile(fileName) {
            try {
                // Create download link
                const link = document.createElement('a');
                link.href = `/downloads/${fileName}`;
                link.download = fileName;
                link.style.display = 'none';
                
                // Set appropriate MIME type
                const extension = fileName.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'pdf':
                        link.type = 'application/pdf';
                        break;
                    case 'docx':
                        link.type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        link.type = 'image/jpeg';
                        break;
                    case 'png':
                        link.type = 'image/png';
                        break;
                    default:
                        link.type = 'application/octet-stream';
                }
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                console.log(`✅ Download initiated for ${fileName}`);
                showNotification(`Download started: ${fileName}`, 'success');
            } catch (error) {
                console.error(`❌ Download failed for ${fileName}:`, error);
                showNotification(`Download failed: ${fileName}`, 'error');
            }
        }

        function showNotification(message, type) {
            // Remove existing notifications
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                background: ${type === 'success' ? '#28a745' : '#dc3545'};
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        async function checkFileAvailability(fileName) {
            try {
                const response = await fetch(`/downloads/${fileName}`, { method: 'HEAD' });
                return {
                    available: response.ok,
                    size: response.headers.get('content-length'),
                    type: response.headers.get('content-type')
                };
            } catch (error) {
                return { available: false, error: error.message };
            }
        }

        async function renderFileList() {
            const fileListDiv = document.getElementById('file-list');
            fileListDiv.innerHTML = '<p>Checking file availability...</p>';
            
            const fileElements = [];
            
            for (const file of testFiles) {
                const availability = await checkFileAvailability(file.name);
                
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';
                
                if (availability.available) {
                    fileDiv.innerHTML = `
                        <div>
                            <strong>${file.name}</strong> (${file.type})
                            <span class="success">✅ Available</span>
                        </div>
                        <div>
                            Size: ${availability.size || 'unknown'} bytes | 
                            Type: ${availability.type || 'unknown'}
                        </div>
                        <button onclick="downloadFile('${file.name}')">Download</button>
                    `;
                } else {
                    fileDiv.innerHTML = `
                        <div>
                            <strong>${file.name}</strong> (${file.type})
                            <span class="error">❌ Not Available</span>
                        </div>
                        <div>Error: ${availability.error || 'File not found'}</div>
                    `;
                }
                
                fileElements.push(fileDiv);
            }
            
            fileListDiv.innerHTML = '';
            fileElements.forEach(el => fileListDiv.appendChild(el));
        }

        // Make downloadFile available globally
        window.downloadFile = downloadFile;

        // Load file list on page load
        window.addEventListener('load', renderFileList);
    </script>
</body>
</html>
