<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug File Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>File Integration Debug Tool</h1>
    
    <div class="test-section">
        <h2>Test 1: Check File Accessibility</h2>
        <button onclick="testFileAccess()">Test File Access</button>
        <div id="file-access-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Test File Download</h2>
        <button onclick="testFileDownload()">Test Download CV1.docx</button>
        <div id="download-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: List Available Files</h2>
        <button onclick="listFiles()">List Files</button>
        <div id="file-list-results"></div>
    </div>

    <script>
        const testFiles = [
            'CV1.docx',
            'CV2.pdf', 
            'cert1.jpg',
            'cert2.png',
            'testimonial1.docx'
        ];

        async function testFileAccess() {
            const resultsDiv = document.getElementById('file-access-results');
            resultsDiv.innerHTML = '<p>Testing file access...</p>';
            
            const results = [];
            
            for (const fileName of testFiles) {
                try {
                    const response = await fetch(`/downloads/${fileName}`, { method: 'HEAD' });
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        const type = response.headers.get('content-type');
                        results.push(`<span class="success">✅ ${fileName} - Size: ${size} bytes, Type: ${type || 'unknown'}</span>`);
                    } else {
                        results.push(`<span class="error">❌ ${fileName} - Status: ${response.status}</span>`);
                    }
                } catch (error) {
                    results.push(`<span class="error">❌ ${fileName} - Error: ${error.message}</span>`);
                }
            }
            
            resultsDiv.innerHTML = results.join('<br>');
        }

        async function testFileDownload() {
            const resultsDiv = document.getElementById('download-results');
            resultsDiv.innerHTML = '<p>Testing file download...</p>';
            
            try {
                // Create a download link
                const link = document.createElement('a');
                link.href = '/downloads/CV1.docx';
                link.download = 'CV1.docx';
                link.style.display = 'none';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                resultsDiv.innerHTML = '<span class="success">✅ Download initiated for CV1.docx</span>';
            } catch (error) {
                resultsDiv.innerHTML = `<span class="error">❌ Download failed: ${error.message}</span>`;
            }
        }

        async function listFiles() {
            const resultsDiv = document.getElementById('file-list-results');
            resultsDiv.innerHTML = '<p>Listing files...</p>';
            
            const fileList = [];
            
            for (const fileName of testFiles) {
                try {
                    const response = await fetch(`/downloads/${fileName}`, { method: 'HEAD' });
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        const lastModified = response.headers.get('last-modified');
                        const contentType = response.headers.get('content-type');
                        
                        fileList.push({
                            name: fileName,
                            size: size ? parseInt(size) : 0,
                            lastModified: lastModified,
                            contentType: contentType,
                            accessible: true
                        });
                    }
                } catch (error) {
                    fileList.push({
                        name: fileName,
                        error: error.message,
                        accessible: false
                    });
                }
            }
            
            const html = fileList.map(file => {
                if (file.accessible) {
                    return `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                            <strong>${file.name}</strong><br>
                            Size: ${file.size} bytes<br>
                            Type: ${file.contentType || 'unknown'}<br>
                            Modified: ${file.lastModified || 'unknown'}
                        </div>
                    `;
                } else {
                    return `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #f00;">
                            <strong>${file.name}</strong> - <span class="error">Error: ${file.error}</span>
                        </div>
                    `;
                }
            }).join('');
            
            resultsDiv.innerHTML = html;
        }

        // Auto-run file access test on page load
        window.addEventListener('load', () => {
            testFileAccess();
        });
    </script>
</body>
</html>
