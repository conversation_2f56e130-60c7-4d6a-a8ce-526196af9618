<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>File Integration Test</h1>
    
    <div class="test-section">
        <h2>Test File Integration Logic</h2>
        <button onclick="testIntegration()">Test Integration</button>
        <div id="integration-results"></div>
    </div>

    <script type="module">
        // Mock the file manager functions for testing
        const SUPPORTED_FILE_TYPES = {
            pdf: {
                mimeType: 'application/pdf',
                extensions: ['.pdf'],
                maxSize: 10 * 1024 * 1024,
            },
            docx: {
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                extensions: ['.docx'],
                maxSize: 5 * 1024 * 1024,
            },
            jpeg: {
                mimeType: 'image/jpeg',
                extensions: ['.jpg', '.jpeg'],
                maxSize: 5 * 1024 * 1024,
            },
            png: {
                mimeType: 'image/png',
                extensions: ['.png'],
                maxSize: 5 * 1024 * 1024,
            }
        };

        const FILE_TYPE_MAPPING = {
            cv: {
                patterns: [/^cv/i, /^resume/i, /curriculum/i],
                label: 'Attach your CV',
                priority: 1
            },
            cover_letter: {
                patterns: [/cover.*letter/i, /^letter/i, /motivation/i],
                label: 'Attach your Cover Letter',
                priority: 2
            },
            certificate: {
                patterns: [/cert/i, /certificate/i, /diploma/i, /degree/i],
                label: 'Attach a certified copy of your certificate',
                priority: 3
            },
            testimonial: {
                patterns: [/testimonial/i, /reference/i, /recommendation/i],
                label: 'Attach a certified copy of your testimonial',
                priority: 4
            }
        };

        function validateFile(fileName, fileSize) {
            const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
            
            let fileType = null;
            let supportedType = null;
            
            for (const [type, config] of Object.entries(SUPPORTED_FILE_TYPES)) {
                if (config.extensions.includes(extension)) {
                    fileType = type;
                    supportedType = config;
                    break;
                }
            }
            
            if (!fileType) {
                return {
                    isValid: false,
                    error: `Unsupported file type: ${extension}`,
                    fileName,
                    fileSize
                };
            }
            
            if (fileSize > supportedType.maxSize) {
                return {
                    isValid: false,
                    error: `File size exceeds maximum allowed size`,
                    fileName,
                    fileSize
                };
            }
            
            return {
                isValid: true,
                fileType,
                mimeType: supportedType.mimeType,
                fileName,
                fileSize
            };
        }

        function determineAttachmentType(fileName) {
            const baseName = fileName.toLowerCase().replace(/\.[^/.]+$/, '');
            
            const sortedTypes = Object.entries(FILE_TYPE_MAPPING)
                .sort(([,a], [,b]) => a.priority - b.priority);
            
            for (const [type, config] of sortedTypes) {
                for (const pattern of config.patterns) {
                    if (pattern.test(baseName)) {
                        return type;
                    }
                }
            }
            
            if (baseName.includes('cv') || baseName.includes('resume')) return 'cv';
            if (baseName.includes('cover') || baseName.includes('letter')) return 'cover_letter';
            if (baseName.includes('cert') || baseName.includes('certificate')) return 'certificate';
            if (baseName.includes('testimonial') || baseName.includes('reference')) return 'testimonial';
            
            return 'certificate';
        }

        async function scanDownloadsDirectory() {
            const knownFiles = [
                'CV1.docx',
                'CV2.docx', 
                'CV2.pdf',
                'CV3.docx',
                'cert1.jpg',
                'cert2.png',
                'cert3.jpg',
                'cert3.pdf',
                'testimonial1.docx',
                'testimonial2.docx',
                'testimonial3.docx'
            ];
            
            const fileInfos = [];
            
            for (const fileName of knownFiles) {
                try {
                    const response = await fetch(`/downloads/${fileName}`, { method: 'HEAD' });
                    
                    if (response.ok) {
                        const contentLength = response.headers.get('content-length');
                        const fileSize = contentLength ? parseInt(contentLength) : 100000;
                        
                        const validation = validateFile(fileName, fileSize);
                        
                        if (validation.isValid) {
                            const attachmentType = determineAttachmentType(fileName);
                            
                            fileInfos.push({
                                name: fileName,
                                size: fileSize,
                                type: attachmentType,
                                mimeType: validation.mimeType,
                                path: `/downloads/${fileName}`,
                                isValid: true
                            });
                        } else {
                            console.warn(`File validation failed for ${fileName}:`, validation.error);
                        }
                    }
                } catch (error) {
                    console.warn(`Could not access file ${fileName}:`, error.message);
                }
            }
            
            return fileInfos;
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
        }

        window.testIntegration = async function() {
            const resultsDiv = document.getElementById('integration-results');
            resultsDiv.innerHTML = '<p>Testing file integration...</p>';
            
            try {
                console.log('Starting file integration test...');
                
                // Step 1: Scan downloads directory
                const files = await scanDownloadsDirectory();
                console.log('Found files:', files);
                
                // Step 2: Create attachments
                const attachments = files.map(file => ({
                    name: file.name,
                    type: file.type,
                    size: formatFileSize(file.size),
                    label: `Attach your ${file.type}`,
                    mimeType: file.mimeType,
                    path: file.path
                }));
                
                console.log('Created attachments:', attachments);
                
                // Display results
                const html = `
                    <div class="success">✅ Integration test completed!</div>
                    <h3>Found ${files.length} valid files:</h3>
                    <pre>${JSON.stringify(files, null, 2)}</pre>
                    <h3>Created ${attachments.length} attachments:</h3>
                    <pre>${JSON.stringify(attachments, null, 2)}</pre>
                `;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Integration test failed:', error);
                resultsDiv.innerHTML = `<div class="error">❌ Integration test failed: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
