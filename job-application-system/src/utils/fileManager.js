// File management utility for scanning, validating, and integrating files from downloads directory
import { formatFileSize } from './fileDownload.js';

// Supported file types and their MIME types
export const SUPPORTED_FILE_TYPES = {
  pdf: {
    mimeType: 'application/pdf',
    extensions: ['.pdf'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  docx: {
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    extensions: ['.docx'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
  jpeg: {
    mimeType: 'image/jpeg',
    extensions: ['.jpg', '.jpeg'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
  png: {
    mimeType: 'image/png',
    extensions: ['.png'],
    maxSize: 5 * 1024 * 1024, // 5MB
  }
};

// File type mapping based on naming conventions
export const FILE_TYPE_MAPPING = {
  cv: {
    patterns: [/^cv/i, /^resume/i, /curriculum/i],
    label: 'Attach your CV',
    priority: 1
  },
  cover_letter: {
    patterns: [/cover.*letter/i, /^letter/i, /motivation/i],
    label: 'Attach your Cover Letter',
    priority: 2
  },
  certificate: {
    patterns: [/cert/i, /certificate/i, /diploma/i, /degree/i],
    label: 'Attach a certified copy of your certificate',
    priority: 3
  },
  testimonial: {
    patterns: [/testimonial/i, /reference/i, /recommendation/i],
    label: 'Attach a certified copy of your testimonial',
    priority: 4
  }
};

/**
 * Validates a file based on extension, size, and MIME type
 * @param {string} fileName - The name of the file
 * @param {number} fileSize - The size of the file in bytes
 * @returns {Object} Validation result with isValid, fileType, mimeType, and error
 */
export const validateFile = (fileName, fileSize) => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  // Find matching file type
  let fileType = null;
  let supportedType = null;
  
  for (const [type, config] of Object.entries(SUPPORTED_FILE_TYPES)) {
    if (config.extensions.includes(extension)) {
      fileType = type;
      supportedType = config;
      break;
    }
  }
  
  if (!fileType) {
    return {
      isValid: false,
      error: `Unsupported file type: ${extension}`,
      fileName,
      fileSize
    };
  }
  
  if (fileSize > supportedType.maxSize) {
    return {
      isValid: false,
      error: `File size exceeds maximum allowed size of ${formatFileSize(supportedType.maxSize.toString())}`,
      fileName,
      fileSize
    };
  }
  
  return {
    isValid: true,
    fileType,
    mimeType: supportedType.mimeType,
    fileName,
    fileSize
  };
};

/**
 * Determines the attachment type based on file name patterns
 * @param {string} fileName - The name of the file
 * @returns {string} The attachment type (cv, cover_letter, certificate, testimonial)
 */
export const determineAttachmentType = (fileName) => {
  const baseName = fileName.toLowerCase().replace(/\.[^/.]+$/, ''); // Remove extension
  
  // Sort by priority to ensure consistent mapping
  const sortedTypes = Object.entries(FILE_TYPE_MAPPING)
    .sort(([,a], [,b]) => a.priority - b.priority);
  
  for (const [type, config] of sortedTypes) {
    for (const pattern of config.patterns) {
      if (pattern.test(baseName)) {
        return type;
      }
    }
  }
  
  // Default fallback based on common patterns
  if (baseName.includes('cv') || baseName.includes('resume')) return 'cv';
  if (baseName.includes('cover') || baseName.includes('letter')) return 'cover_letter';
  if (baseName.includes('cert') || baseName.includes('certificate')) return 'certificate';
  if (baseName.includes('testimonial') || baseName.includes('reference')) return 'testimonial';
  
  // Default to certificate for unknown files
  return 'certificate';
};

/**
 * Gets the appropriate label for an attachment type with copy number
 * @param {string} attachmentType - The type of attachment
 * @param {number} copyNumber - The copy number for multiple files of same type
 * @returns {string} The formatted label
 */
export const getAttachmentLabel = (attachmentType, copyNumber = 1) => {
  const baseLabel = FILE_TYPE_MAPPING[attachmentType]?.label || 'Attach file';
  
  if (attachmentType === 'certificate' || attachmentType === 'testimonial') {
    return `${baseLabel} (Copy ${copyNumber})`;
  }
  
  return baseLabel;
};

/**
 * Scans the downloads directory and returns file information
 * This is a mock function since we can't directly access the file system from the browser
 * In a real implementation, this would be handled by a backend service
 * @returns {Promise<Array>} Array of file information objects
 */
export const scanDownloadsDirectory = async () => {
  // Mock file list based on what we know is in the downloads directory
  const knownFiles = [
    'CV1.docx',
    'CV2.docx', 
    'CV2.pdf',
    'CV3.docx',
    'cert1.jpg',
    'cert2.png',
    'cert3.jpg',
    'cert3.pdf',
    'testimonial1.docx',
    'testimonial2.docx',
    'testimonial3.docx'
  ];
  
  const fileInfos = [];
  
  for (const fileName of knownFiles) {
    try {
      // Try to get file info via HEAD request
      const response = await fetch(`./downloads/${fileName}`, { method: 'HEAD' });
      
      if (response.ok) {
        const contentLength = response.headers.get('content-length');
        const fileSize = contentLength ? parseInt(contentLength) : estimateFileSize(fileName);
        
        const validation = validateFile(fileName, fileSize);
        
        if (validation.isValid) {
          const attachmentType = determineAttachmentType(fileName);
          
          fileInfos.push({
            name: fileName,
            size: fileSize,
            type: attachmentType,
            mimeType: validation.mimeType,
            path: `./downloads/${fileName}`,
            isValid: true
          });
        } else {
          console.warn(`File validation failed for ${fileName}:`, validation.error);
        }
      }
    } catch (error) {
      console.warn(`Could not access file ${fileName}:`, error.message);
    }
  }
  
  return fileInfos;
};

/**
 * Estimates file size based on file name and type (fallback when content-length is not available)
 * @param {string} fileName - The name of the file
 * @returns {number} Estimated file size in bytes
 */
const estimateFileSize = (fileName) => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  // Rough estimates based on typical file sizes
  switch (extension) {
    case '.pdf':
      return 200 * 1024; // 200KB
    case '.docx':
      return 150 * 1024; // 150KB
    case '.jpg':
    case '.jpeg':
      return 180 * 1024; // 180KB
    case '.png':
      return 160 * 1024; // 160KB
    default:
      return 100 * 1024; // 100KB
  }
};

/**
 * Groups files by attachment type and assigns copy numbers
 * @param {Array} fileInfos - Array of file information objects
 * @returns {Array} Array of attachment objects ready for use in applicant data
 */
export const createAttachmentsFromFiles = (fileInfos) => {
  const groupedFiles = {};
  
  // Group files by attachment type
  fileInfos.forEach(file => {
    if (!groupedFiles[file.type]) {
      groupedFiles[file.type] = [];
    }
    groupedFiles[file.type].push(file);
  });
  
  const attachments = [];
  
  // Create attachment objects with proper labels and copy numbers
  Object.entries(groupedFiles).forEach(([type, files]) => {
    files.forEach((file, index) => {
      const copyNumber = index + 1;
      const label = getAttachmentLabel(type, copyNumber);
      
      attachments.push({
        name: file.name,
        type: type,
        size: formatFileSize(file.size.toString()),
        label: label,
        mimeType: file.mimeType,
        path: file.path
      });
    });
  });
  
  // Sort attachments by type priority and then by copy number
  return attachments.sort((a, b) => {
    const priorityA = FILE_TYPE_MAPPING[a.type]?.priority || 999;
    const priorityB = FILE_TYPE_MAPPING[b.type]?.priority || 999;
    
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }
    
    // If same type, sort by name to maintain consistent order
    return a.name.localeCompare(b.name);
  });
};

/**
 * Main function to scan downloads directory and create attachment data
 * @returns {Promise<Array>} Array of attachment objects
 */
export const integrateDownloadFiles = async () => {
  try {
    const fileInfos = await scanDownloadsDirectory();
    const attachments = createAttachmentsFromFiles(fileInfos);
    
    console.log(`Successfully integrated ${attachments.length} files from downloads directory`);
    return attachments;
  } catch (error) {
    console.error('Error integrating download files:', error);
    return [];
  }
};
