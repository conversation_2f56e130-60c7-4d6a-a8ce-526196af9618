// Service for integrating files from downloads directory into applicant data
import { integrateDownloadFiles, createAttachmentsFromFiles, scanDownloadsDirectory } from '../utils/fileManager.js';
import { formatFileSize } from '../utils/fileDownload.js';

/**
 * Updates applicant data with real files from downloads directory
 * @param {Array} applicantsData - Original applicant data array
 * @returns {Promise<Array>} Updated applicant data with real file attachments
 */
export const updateApplicantsWithRealFiles = async (applicantsData) => {
  try {
    console.log('Starting file integration process...');
    
    // Get all available files from downloads directory
    const availableFiles = await scanDownloadsDirectory();
    
    if (availableFiles.length === 0) {
      console.warn('No valid files found in downloads directory, keeping original data');
      return applicantsData;
    }
    
    console.log(`Found ${availableFiles.length} valid files in downloads directory`);
    
    // Create a pool of attachments from available files
    const attachmentPool = createAttachmentsFromFiles(availableFiles);
    
    // Update each applicant with real files
    const updatedApplicants = applicantsData.map((applicant, index) => {
      const updatedAttachments = assignFilesToApplicant(attachmentPool, applicant, index);
      
      return {
        ...applicant,
        attachments: updatedAttachments
      };
    });
    
    console.log('File integration completed successfully');
    return updatedApplicants;
    
  } catch (error) {
    console.error('Error updating applicants with real files:', error);
    // Return original data if integration fails
    return applicantsData;
  }
};

/**
 * Assigns files to a specific applicant based on available files and applicant index
 * @param {Array} attachmentPool - Pool of available attachment objects
 * @param {Object} applicant - The applicant object
 * @param {number} applicantIndex - Index of the applicant (for file distribution)
 * @returns {Array} Array of attachment objects for the applicant
 */
const assignFilesToApplicant = (attachmentPool, applicant, applicantIndex) => {
  const assignedAttachments = [];
  
  // Group available files by type
  const filesByType = groupFilesByType(attachmentPool);
  
  // Define required attachment types for each applicant
  const requiredTypes = ['cv', 'cover_letter', 'certificate', 'testimonial'];
  
  requiredTypes.forEach(type => {
    const availableFilesOfType = filesByType[type] || [];
    
    if (type === 'cv' || type === 'cover_letter') {
      // For CV and cover letter, assign one file per applicant
      const fileIndex = applicantIndex % availableFilesOfType.length;
      if (availableFilesOfType[fileIndex]) {
        assignedAttachments.push(availableFilesOfType[fileIndex]);
      }
    } else {
      // For certificates and testimonials, assign multiple files (up to 3)
      const maxFiles = Math.min(3, availableFilesOfType.length);
      for (let i = 0; i < maxFiles; i++) {
        const fileIndex = (applicantIndex + i) % availableFilesOfType.length;
        if (availableFilesOfType[fileIndex]) {
          const attachment = { ...availableFilesOfType[fileIndex] };
          // Update label to include copy number
          if (i > 0) {
            attachment.label = attachment.label.replace(/\(Copy \d+\)/, `(Copy ${i + 1})`);
          }
          assignedAttachments.push(attachment);
        }
      }
    }
  });
  
  // If no real files are available for some types, create fallback attachments
  if (assignedAttachments.length === 0) {
    return createFallbackAttachments(applicant);
  }
  
  // Ensure we have at least the minimum required attachments
  return ensureMinimumAttachments(assignedAttachments, applicant);
};

/**
 * Groups files by their attachment type
 * @param {Array} attachmentPool - Pool of attachment objects
 * @returns {Object} Object with files grouped by type
 */
const groupFilesByType = (attachmentPool) => {
  const grouped = {};
  
  attachmentPool.forEach(attachment => {
    if (!grouped[attachment.type]) {
      grouped[attachment.type] = [];
    }
    grouped[attachment.type].push(attachment);
  });
  
  return grouped;
};

/**
 * Creates fallback attachments when no real files are available
 * @param {Object} applicant - The applicant object
 * @returns {Array} Array of fallback attachment objects
 */
const createFallbackAttachments = (applicant) => {
  return [
    { 
      name: "CV.pdf", 
      type: "cv", 
      size: "245 KB", 
      label: "Attach your CV" 
    },
    { 
      name: "Cover_Letter.pdf", 
      type: "cover_letter", 
      size: "156 KB", 
      label: "Attach your Cover Letter" 
    },
    { 
      name: "cert_copy1.pdf", 
      type: "certificate", 
      size: "180 KB", 
      label: "Attach a certified copy of your certificate (Copy 1)" 
    },
    { 
      name: "testimonial_1.pdf", 
      type: "testimonial", 
      size: "165 KB", 
      label: "Attach a certified copy of your testimonial (Copy 1)" 
    }
  ];
};

/**
 * Ensures minimum required attachments are present
 * @param {Array} attachments - Current attachments
 * @param {Object} applicant - The applicant object
 * @returns {Array} Array of attachments with minimum requirements met
 */
const ensureMinimumAttachments = (attachments, applicant) => {
  const typeCount = {};
  attachments.forEach(att => {
    typeCount[att.type] = (typeCount[att.type] || 0) + 1;
  });
  
  // Ensure at least one CV
  if (!typeCount.cv) {
    attachments.unshift({
      name: "CV.pdf",
      type: "cv",
      size: "245 KB",
      label: "Attach your CV"
    });
  }
  
  // Ensure at least one cover letter
  if (!typeCount.cover_letter) {
    attachments.push({
      name: "Cover_Letter.pdf",
      type: "cover_letter",
      size: "156 KB",
      label: "Attach your Cover Letter"
    });
  }
  
  return attachments;
};

/**
 * Validates that all attachments have required properties
 * @param {Array} attachments - Array of attachment objects
 * @returns {Array} Array of validated attachment objects
 */
export const validateAttachments = (attachments) => {
  return attachments.filter(attachment => {
    const hasRequiredProps = attachment.name && 
                           attachment.type && 
                           attachment.size && 
                           attachment.label;
    
    if (!hasRequiredProps) {
      console.warn('Invalid attachment found:', attachment);
      return false;
    }
    
    return true;
  });
};

/**
 * Gets statistics about file integration
 * @param {Array} originalData - Original applicant data
 * @param {Array} updatedData - Updated applicant data
 * @returns {Object} Statistics object
 */
export const getIntegrationStats = (originalData, updatedData) => {
  const stats = {
    totalApplicants: updatedData.length,
    totalAttachments: 0,
    realFiles: 0,
    fallbackFiles: 0,
    fileTypes: {}
  };
  
  updatedData.forEach(applicant => {
    applicant.attachments.forEach(attachment => {
      stats.totalAttachments++;
      
      // Check if it's a real file (has path property) or fallback
      if (attachment.path && attachment.path.includes('./downloads/')) {
        stats.realFiles++;
      } else {
        stats.fallbackFiles++;
      }
      
      // Count file types
      stats.fileTypes[attachment.type] = (stats.fileTypes[attachment.type] || 0) + 1;
    });
  });
  
  return stats;
};

/**
 * Main function to integrate files and update applicant data
 * @param {Array} applicantsData - Original applicant data
 * @returns {Promise<Object>} Object containing updated data and statistics
 */
export const integrateFilesIntoApplicantData = async (applicantsData) => {
  try {
    const updatedData = await updateApplicantsWithRealFiles(applicantsData);
    const stats = getIntegrationStats(applicantsData, updatedData);
    
    console.log('File Integration Statistics:', stats);
    
    return {
      success: true,
      data: updatedData,
      stats: stats,
      message: `Successfully integrated ${stats.realFiles} real files and ${stats.fallbackFiles} fallback files`
    };
    
  } catch (error) {
    console.error('File integration failed:', error);
    
    return {
      success: false,
      data: applicantsData,
      error: error.message,
      message: 'File integration failed, using original data'
    };
  }
};
