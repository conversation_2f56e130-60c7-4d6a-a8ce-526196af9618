// Sample applicant data with updated structure for two-page workflow
import { integrateFilesIntoApplicantData } from '../services/fileIntegrationService.js';

// Base applicant data (will be enhanced with real files from downloads directory)
const baseApplicantsData = [
  {
    id: 1,
    uid: "123456",
    firstName: "<PERSON>",
    lastName: "Do<PERSON>",
    email: "johndo<PERSON>@email.com",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "CLOUD INFRASTRUCTURE ENGINEER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "BA / BSc / HND",
    applicationDate: "2024-01-15",
    status: "Under Review",
    attachments: [
      { name: "CV.pdf", type: "cv", size: "245 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "156 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "180 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "220 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "195 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "165 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "175 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "155 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 2,
    uid: "234567",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "SOFTWARE DEVELOPER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "Msc / BSc / HND",
    applicationDate: "2024-01-18",
    status: "Under Review",
    attachments: [
      { name: "CV.docx", type: "cv", size: "198 KB", label: "Attach your CV" },
      { name: "Cover_Letter.docx", type: "cover_letter", size: "142 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.jpg", type: "certificate", size: "190 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.pdf", type: "certificate", size: "210 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.jpg", type: "certificate", size: "185 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "160 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "170 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "150 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 3,
    uid: "345678",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "DATA ANALYST",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "Diploma",
    applicationDate: "2024-01-20",
    status: "Under Review",
    attachments: [
      { name: "CV.pdf", type: "cv", size: "267 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "178 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "200 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "230 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "205 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "170 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "180 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "165 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 4,
    uid: "456789",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "CLOUD INFRASTRUCTURE ENGINEER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "BA / BSc / HND",
    applicationDate: "2024-01-25",
    status: "Under Review",
    attachments: [
      { name: "CV.pdf", type: "cv", size: "255 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "166 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "190 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "240 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "205 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "175 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "185 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "175 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 5,
    uid: "567890",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "CLOUD INFRASTRUCTURE ENGINEER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "Msc / BSc / HND",
    applicationDate: "2024-01-28",
    status: "Under Review",
    attachments: [
      { name: "CV.docx", type: "cv", size: "248 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "158 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "185 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "225 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "200 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "170 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "180 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "160 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 6,
    uid: "678901",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "DEVOPS ENGINEER",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "Diploma",
    applicationDate: "2024-02-01",
    status: "Under Review",
    attachments: [
      { name: "CV.pdf", type: "cv", size: "275 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "172 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "195 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "235 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "210 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "180 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "190 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "170 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
  {
    id: 7,
    uid: "789012",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "0712345648",
    currentResidence: "Nairobi",
    jobTitle: "SYSTEM ADMINISTRATOR",
    jobVacancyNumber: "xxxx/xxx/xxx",
    levelOfEducation: "BA / BSc / HND",
    applicationDate: "2024-02-05",
    status: "Under Review",
    attachments: [
      { name: "CV.pdf", type: "cv", size: "260 KB", label: "Attach your CV" },
      { name: "Cover_Letter.pdf", type: "cover_letter", size: "164 KB", label: "Attach your Cover Letter" },
      { name: "cert_copy1.pdf", type: "certificate", size: "188 KB", label: "Attach a certified copy of your certificate (Copy 1)" },
      { name: "cert_copy2.jpg", type: "certificate", size: "228 KB", label: "Attach a certified copy of your certificate (Copy 2)" },
      { name: "cert_copy3.pdf", type: "certificate", size: "198 KB", label: "Attach a certified copy of your certificate (Copy 3)" },
      { name: "testimonial_1.pdf", type: "testimonial", size: "168 KB", label: "Attach a certified copy of your testimonial (Copy 1)" },
      { name: "testimonial_2.pdf", type: "testimonial", size: "178 KB", label: "Attach a certified copy of your testimonial (Copy 2)" },
      { name: "testimonial_3.pdf", type: "testimonial", size: "158 KB", label: "Attach a certified copy of your testimonial (Copy 3)" }
    ]
  },
];

// Status options for filtering
export const statusOptions = [
  "All",
  "Pending",
  "Under Review",
  "Interview Scheduled",
  "Hired",
  "Rejected"
];

// Job title options for filtering
export const jobTitleOptions = [
  "All",
  "CLOUD INFRASTRUCTURE ENGINEER",
  "SOFTWARE DEVELOPER",
  "DATA ANALYST",
  "DEVOPS ENGINEER",
  "SYSTEM ADMINISTRATOR"
];

// Enhanced applicants data with real files from downloads directory
let enhancedApplicantsData = null;

/**
 * Gets applicant data with integrated files from downloads directory
 * @returns {Promise<Array>} Enhanced applicant data with real files
 */
export const getApplicantsData = async () => {
  if (enhancedApplicantsData) {
    return enhancedApplicantsData;
  }

  try {
    console.log('Integrating files from downloads directory...');
    const result = await integrateFilesIntoApplicantData(baseApplicantsData);

    if (result.success) {
      enhancedApplicantsData = result.data;
      console.log(result.message);
      console.log('Integration stats:', result.stats);
    } else {
      console.warn('File integration failed:', result.message);
      enhancedApplicantsData = baseApplicantsData;
    }

    return enhancedApplicantsData;
  } catch (error) {
    console.error('Error getting enhanced applicants data:', error);
    return baseApplicantsData;
  }
};

// Export the base data for backward compatibility
export const applicantsData = baseApplicantsData;
