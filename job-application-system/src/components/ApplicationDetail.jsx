import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { applicantsData } from '../data/applicantsData';
import { downloadFile } from '../utils/fileDownload';
import './ApplicationDetail.css';

const ApplicationDetail = () => {
  const { uid } = useParams();
  const navigate = useNavigate();
  
  // Find the applicant by UID
  const applicant = applicantsData.find(app => app.uid === uid);
  
  if (!applicant) {
    return (
      <div className="application-detail">
        <div className="detail-header">
          <button onClick={() => navigate('/')} className="back-btn">
            ← Back to Applications
          </button>
        </div>
        <div className="detail-content">
          <div className="error-message">
            <h2>Application Not Found</h2>
            <p>The application with UID {uid} could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  const handleFileDownload = async (fileName) => {
    await downloadFile(fileName, `${applicant.firstName}_${applicant.lastName}`);
  };

  return (
    <div className="application-detail">
      <div className="detail-header">
        <button onClick={() => navigate('/')} className="back-btn">
          ← Back to Applications
        </button>
        <h1 className="detail-title">#{applicant.uid} - {applicant.jobTitle}</h1>
      </div>

      <div className="detail-content">
        <div className="detail-card">
          <div className="applicant-info">
            <div className="info-row">
              <label>First Name:</label>
              <span>{applicant.firstName}</span>
            </div>
            
            <div className="info-row">
              <label>Last Name:</label>
              <span>{applicant.lastName}</span>
            </div>
            
            <div className="info-row">
              <label>Email Address:</label>
              <span>{applicant.email}</span>
            </div>
            
            <div className="info-row">
              <label>Phone Number:</label>
              <span>{applicant.phone}</span>
            </div>
            
            <div className="info-row">
              <label>Current Residence:</label>
              <span>{applicant.currentResidence}</span>
            </div>
            
            <div className="info-row">
              <label>Job Vacancy Number:</label>
              <span>{applicant.jobVacancyNumber}</span>
            </div>
            
            <div className="info-row">
              <label>Level of Education:</label>
              <span>{applicant.levelOfEducation}</span>
            </div>
          </div>

          <div className="attachments-section">
            <h3>Attachments:</h3>
            <div className="attachments-list">
              {applicant.attachments.map((attachment, index) => (
                <div key={index} className="attachment-item">
                  <div className="attachment-info">
                    <span className="attachment-bullet">•</span>
                    <span className="attachment-label">{attachment.label}</span>
                    <span className="attachment-separator">—</span>
                    <button
                      onClick={() => handleFileDownload(attachment.name)}
                      className="attachment-link"
                      title={`Download ${attachment.name} (${attachment.size})`}
                    >
                      Attachment
                    </button>
                  </div>
                  <div className="attachment-meta">
                    <span className="file-name">{attachment.name}</span>
                    <span className="file-size">({attachment.size})</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="application-meta">
            <div className="meta-row">
              <label>Application Date:</label>
              <span>{new Date(applicant.applicationDate).toLocaleDateString()}</span>
            </div>
            
            <div className="meta-row">
              <label>Status:</label>
              <span className={`status-badge ${getStatusClass(applicant.status)}`}>
                {applicant.status}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function for status styling
const getStatusClass = (status) => {
  switch (status) {
    case 'Hired': return 'status-hired';
    case 'Interview Scheduled': return 'status-interview';
    case 'Under Review': return 'status-review';
    case 'Rejected': return 'status-rejected';
    default: return 'status-pending';
  }
};

export default ApplicationDetail;
