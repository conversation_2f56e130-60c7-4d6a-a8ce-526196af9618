import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { applicantsData, statusOptions, positionOptions } from '../data/applicantsData';
import { downloadFile, getFileTypeIcon } from '../utils/fileDownload';
import './Dashboard.css';

const Dashboard = () => {
  const { logout } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [positionFilter, setPositionFilter] = useState('All');
  const [sortField, setSortField] = useState('applicationDate');
  const [sortDirection, setSortDirection] = useState('desc');

  // Filter and sort applicants
  const filteredApplicants = applicantsData
    .filter(applicant => {
      const matchesSearch = applicant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           applicant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           applicant.position.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'All' || applicant.status === statusFilter;
      const matchesPosition = positionFilter === 'All' || applicant.position === positionFilter;
      
      return matchesSearch && matchesStatus && matchesPosition;
    })
    .sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      if (sortField === 'applicationDate') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }
      
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleFileDownload = async (applicantName, fileName) => {
    await downloadFile(fileName, applicantName);
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'Hired': return 'status-hired';
      case 'Interview Scheduled': return 'status-interview';
      case 'Under Review': return 'status-review';
      case 'Rejected': return 'status-rejected';
      default: return 'status-pending';
    }
  };

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-content">
          <h1>Job Application Management System</h1>
          <button onClick={logout} className="logout-btn">Logout</button>
        </div>
      </header>

      <div className="dashboard-content">
        <div className="filters-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search applicants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="filter-controls">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={positionFilter}
              onChange={(e) => setPositionFilter(e.target.value)}
              className="filter-select"
            >
              {positionOptions.map(position => (
                <option key={position} value={position}>{position}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="stats-section">
          <div className="stat-card">
            <h3>Total Applications</h3>
            <p>{applicantsData.length}</p>
          </div>
          <div className="stat-card">
            <h3>Filtered Results</h3>
            <p>{filteredApplicants.length}</p>
          </div>
          <div className="stat-card">
            <h3>Hired</h3>
            <p>{applicantsData.filter(a => a.status === 'Hired').length}</p>
          </div>
          <div className="stat-card">
            <h3>Pending</h3>
            <p>{applicantsData.filter(a => a.status === 'Pending').length}</p>
          </div>
        </div>

        <div className="table-container">
          <table className="applicants-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('name')} className="sortable">
                  Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('email')} className="sortable">
                  Email {sortField === 'email' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('position')} className="sortable">
                  Position {sortField === 'position' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('experience')} className="sortable">
                  Experience {sortField === 'experience' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('applicationDate')} className="sortable">
                  Application Date {sortField === 'applicationDate' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('status')} className="sortable">
                  Status {sortField === 'status' && (sortDirection === 'asc' ? '↑' : '↓')}
                </th>
                <th>Files</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredApplicants.map(applicant => (
                <tr key={applicant.id}>
                  <td className="name-cell">
                    <div className="applicant-name">{applicant.name}</div>
                    <div className="applicant-phone">{applicant.phone}</div>
                  </td>
                  <td>{applicant.email}</td>
                  <td>
                    <div className="position-cell">
                      <div className="position">{applicant.position}</div>
                      <div className="salary">{applicant.salary}</div>
                    </div>
                  </td>
                  <td>
                    <div className="experience-cell">
                      <div className="experience">{applicant.experience}</div>
                      <div className="education">{applicant.education}</div>
                    </div>
                  </td>
                  <td>{new Date(applicant.applicationDate).toLocaleDateString()}</td>
                  <td>
                    <span className={`status-badge ${getStatusClass(applicant.status)}`}>
                      {applicant.status}
                    </span>
                  </td>
                  <td className="files-cell">
                    {applicant.files.map((file, index) => (
                      <button
                        key={index}
                        onClick={() => handleFileDownload(applicant.name, file.name)}
                        className="file-download-btn"
                        title={`Download ${file.name} (${file.size})`}
                      >
                        {getFileTypeIcon(file.type)} {file.type.replace('_', ' ')}
                      </button>
                    ))}
                  </td>
                  <td className="actions-cell">
                    <button className="action-btn view-btn">View</button>
                    <button className="action-btn edit-btn">Edit</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
