.application-detail {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.detail-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.detail-header .back-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-bottom: 15px;
  display: inline-block;
  text-decoration: none;
  max-width: 1200px;
  margin: 0 auto 15px auto;
  margin-left: 20px;
}

.detail-header .back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.detail-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.detail-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 30px 20px;
}

.detail-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.applicant-info {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row label {
  font-weight: 600;
  color: #333;
  min-width: 180px;
  margin-right: 20px;
  flex-shrink: 0;
}

.info-row span {
  color: #666;
  flex: 1;
  word-break: break-word;
}

.attachments-section {
  padding: 30px;
  border-bottom: 1px solid #e9ecef;
}

.attachments-section h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-bullet {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}

.attachment-label {
  color: #333;
  flex: 1;
}

.attachment-separator {
  color: #666;
  margin: 0 5px;
}

.attachment-link {
  background: #667eea;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
}

.attachment-link:hover {
  background: #5a6fd8;
}

.attachment-meta {
  margin-left: 24px;
  font-size: 12px;
  color: #888;
}

.file-name {
  font-family: 'Courier New', monospace;
  margin-right: 8px;
}

.file-size {
  color: #999;
}

.application-meta {
  padding: 30px;
}

.meta-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-row label {
  font-weight: 600;
  color: #333;
  min-width: 180px;
  margin-right: 20px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-review {
  background: #d1ecf1;
  color: #0c5460;
}

.status-interview {
  background: #d4edda;
  color: #155724;
}

.status-hired {
  background: #d1e7dd;
  color: #0f5132;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.error-message {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 40px;
  text-align: center;
}

.error-message h2 {
  color: #dc3545;
  margin-bottom: 15px;
}

.error-message p {
  color: #666;
  margin: 0;
}

/* Loading states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .detail-title {
    font-size: 24px;
    padding: 0 20px;
  }
  
  .detail-content {
    padding: 20px 10px;
  }
  
  .applicant-info,
  .attachments-section,
  .application-meta {
    padding: 20px;
  }
  
  .info-row,
  .meta-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .info-row label,
  .meta-row label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .attachment-info {
    flex-wrap: wrap;
  }
  
  .attachment-label {
    flex-basis: 100%;
    margin-bottom: 5px;
  }
}

@media (max-width: 480px) {
  .detail-header {
    padding: 15px 0;
  }
  
  .detail-title {
    font-size: 20px;
    line-height: 1.3;
  }
  
  .applicant-info,
  .attachments-section,
  .application-meta {
    padding: 15px;
  }
  
  .attachments-section h3 {
    font-size: 18px;
  }
  
  .attachment-item {
    gap: 8px;
  }
  
  .attachment-meta {
    margin-left: 16px;
  }
}
