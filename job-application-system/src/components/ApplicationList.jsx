import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { applicantsData, statusOptions, jobTitleOptions } from '../data/applicantsData';
import './ApplicationList.css';

const ApplicationList = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [jobTitleFilter, setJobTitleFilter] = useState('All');

  // Filter applicants based on search and filters
  const filteredApplicants = applicantsData.filter(applicant => {
    const matchesSearch = 
      applicant.uid.includes(searchTerm) ||
      applicant.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      applicant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      applicant.lastName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'All' || applicant.status === statusFilter;
    const matchesJobTitle = jobTitleFilter === 'All' || applicant.jobTitle === jobTitleFilter;
    
    return matchesSearch && matchesStatus && matchesJobTitle;
  });

  const handleApplicationClick = (uid) => {
    navigate(`/application/${uid}`);
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'Hired': return 'status-hired';
      case 'Interview Scheduled': return 'status-interview';
      case 'Under Review': return 'status-review';
      case 'Rejected': return 'status-rejected';
      default: return 'status-pending';
    }
  };

  return (
    <div className="application-list">
      <header className="list-header">
        <div className="header-content">
          <h1>Job Application Management System</h1>
          <button onClick={logout} className="logout-btn">Logout</button>
        </div>
      </header>

      <div className="list-content">
        <div className="filters-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search by UID, job title, or name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="filter-controls">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={jobTitleFilter}
              onChange={(e) => setJobTitleFilter(e.target.value)}
              className="filter-select"
            >
              {jobTitleOptions.map(jobTitle => (
                <option key={jobTitle} value={jobTitle}>{jobTitle}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="stats-section">
          <div className="stat-card">
            <h3>Total Applications</h3>
            <p>{applicantsData.length}</p>
          </div>
          <div className="stat-card">
            <h3>Filtered Results</h3>
            <p>{filteredApplicants.length}</p>
          </div>
          <div className="stat-card">
            <h3>Pending Review</h3>
            <p>{applicantsData.filter(a => a.status === 'Pending' || a.status === 'Under Review').length}</p>
          </div>
          <div className="stat-card">
            <h3>Interview Scheduled</h3>
            <p>{applicantsData.filter(a => a.status === 'Interview Scheduled').length}</p>
          </div>
        </div>

        <div className="applications-container">
          <h2>Job Applications</h2>
          <div className="applications-list">
            {filteredApplicants.map(applicant => (
              <div
                key={applicant.id}
                className="application-item"
                onClick={() => handleApplicationClick(applicant.uid)}
              >
                <div className="application-info">
                  <div className="application-title">
                    <span className="uid">{applicant.uid}</span>
                    <span className="separator">—</span>
                    <span className="job-title">{applicant.jobTitle}</span>
                    <span className="separator">—</span>
                    <span className="first-name">{applicant.firstName}</span>
                  </div>
                  <div className="application-meta">
                    <span className="email">{applicant.email}</span>
                    <span className="date">Applied: {new Date(applicant.applicationDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="application-status">
                  <span className={`status-badge ${getStatusClass(applicant.status)}`}>
                    {applicant.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          {filteredApplicants.length === 0 && (
            <div className="no-results">
              <p>No applications found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationList;
