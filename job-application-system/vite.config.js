import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  publicDir: 'public',
  server: {
    fs: {
      // Allow serving files from downloads directory
      allow: ['..', '.']
    }
  },
  // Configure static file serving for downloads directory
  define: {
    // This helps with file path resolution
    __DOWNLOADS_PATH__: JSON.stringify('/downloads/')
  }
})
