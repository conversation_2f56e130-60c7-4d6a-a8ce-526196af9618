# Job Application Management System

A modern, responsive web application for managing job applications with advanced file handling capabilities and real file integration from downloads directory.

## 🌟 Features

### 🔐 Authentication System
- Secure login with session management
- Protected routes and components
- Demo credentials: `admin` / `1234`

### 📋 Application Management
- View all job applications in a clean, organized list
- Detailed application view with comprehensive information
- Advanced filtering and search capabilities:
  - Search by UID, name, job title
  - Filter by application status
  - Filter by job title/position
- Real-time statistics dashboard

### 👤 Applicant Information Display
- Personal details (name, email, phone, residence)
- Professional information (job title, education level)
- Application metadata (date, status, vacancy number)
- Comprehensive two-page workflow support

### 📁 Advanced File Management & Integration
- **Real File Integration**: Automatically scans and integrates files from downloads directory
- **Supported File Types**: PDF, DOCX, JPEG, PNG with proper validation
- **Smart File Mapping**: Automatic categorization based on naming conventions
- **8 Standardized Attachment Types**:
  - CV attachment (uses CV1.docx, CV2.pdf, CV3.docx)
  - Cover Letter attachment (reuses CV files with proper labeling)
  - Certificate copies (cert1.jpg, cert2.png, cert3.pdf)
  - Testimonial copies (testimonial1.docx, testimonial2.docx, testimonial3.docx)
- **File Validation**: Size limits, MIME type detection, extension validation
- **Error Handling**: Graceful fallback mechanisms with user notifications
- **Download Priority**: Downloads directory → fallback locations → sample files

### 🧭 Navigation
- React Router-based navigation
- Back button functionality
- URL-based routing for direct access
- Loading states with proper UI feedback

## 🚀 Live Demo

- **Demo URL**: [Will be available after Vercel deployment]
- **Username:** admin
- **Password:** 1234

## 📦 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/job-application-system.git
cd job-application-system
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

## 📁 Project Structure

```
job-application-system/
├── public/
│   ├── downloads/              # Real files for integration
│   │   ├── CV1.docx
│   │   ├── CV2.pdf
│   │   ├── CV3.docx
│   │   ├── cert1.jpg
│   │   ├── cert2.png
│   │   ├── cert3.pdf
│   │   ├── testimonial1.docx
│   │   └── ...
│   ├── sample-files/           # Fallback files
│   └── debug-files.html        # Debug tools
├── src/
│   ├── components/
│   │   ├── ApplicationList.jsx     # Main applications list
│   │   ├── ApplicationDetail.jsx   # Individual application view
│   │   ├── Login.jsx              # Authentication
│   │   └── *.css                  # Component styles
│   ├── contexts/
│   │   └── AuthContext.jsx        # Authentication context
│   ├── data/
│   │   └── applicantsData.js      # Enhanced with file integration
│   ├── services/
│   │   └── fileIntegrationService.js  # File integration logic
│   ├── utils/
│   │   ├── fileManager.js         # File management utilities
│   │   └── fileDownload.js        # Download utilities
│   ├── App.jsx                    # Main app component
│   └── main.jsx                   # App entry point
├── FILE_INTEGRATION_GUIDE.md      # Comprehensive file system guide
├── test-file-integration.js       # Integration testing
└── vite.config.js                 # Vite configuration
```

## 🔧 File Integration System

### Supported File Types
- **PDF** (.pdf) - Max 10MB
- **DOCX** (.docx) - Max 5MB  
- **JPEG** (.jpg, .jpeg) - Max 5MB
- **PNG** (.png) - Max 5MB

### File Mapping Logic
- **CV**: Files matching `CV1`, `CV2`, `CV3` patterns
- **Cover Letter**: Reuses CV files with appropriate labeling
- **Certificate**: Files matching `cert` patterns
- **Testimonial**: Files matching `testimonial` patterns

### Download Priority
1. `/downloads/` directory (primary)
2. `../job-application-system/` directory
3. `../sample-application-files/` directory
4. `/sample-files/` directory (fallback)

## 🧪 Testing & Debug Tools

### Debug Pages
- `/debug-files.html` - File accessibility testing
- `/test-integration.html` - Integration logic testing
- `/test-download.html` - Download functionality testing

### Test Script
```bash
node test-file-integration.js
```

## 🌐 Deployment

### Vercel Deployment
1. Push to GitHub repository
2. Connect repository to Vercel
3. Configure build settings:
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

### Environment Variables
No environment variables required for basic functionality.

## 🛠️ Technologies Used

- **Frontend Framework:** React 18
- **Build Tool:** Vite 7.1.2
- **Routing:** React Router DOM
- **Styling:** CSS3 with modern features
- **State Management:** React Context API
- **File Handling:** Custom utilities with validation
- **Deployment:** Vercel-ready configuration

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (responsive design)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with modern React best practices
- Responsive design principles
- Accessibility considerations
- Performance optimizations
- Real file integration capabilities
- Comprehensive error handling
