# Job Application Management System

A modern two-page web application for managing job applications with admin authentication and comprehensive file management capabilities.

## Features

### 🔐 Admin Authentication
- Secure login page with hardcoded credentials
- Session persistence using localStorage
- Protected application access

### 📋 Two-Page Workflow

#### Page 1: Application List View
- Clean list of job applications showing: `[6-digit UID] — [JOB TITLE] — [FIRST NAME]`
- Example: `123456 — CLOUD INFRASTRUCTURE ENGINEER — John`
- Clickable list items for navigation to detailed view
- Real-time search and filtering capabilities
- Status-based filtering
- Statistics overview cards

#### Page 2: Application Detail View
- Comprehensive applicant information display
- Page title format: `#[UID] - [JOB TITLE]`
- All required fields in specified order:
  - Personal Information (First Name, Last Name, Email, Phone, Residence)
  - Job Information (Job Vacancy Number, Level of Education)
  - 8 Standardized Attachments with download functionality

### 📁 Advanced File Management
- 8 standardized attachment types:
  - CV attachment
  - Cover Letter attachment
  - Certificate copies (3 copies)
  - Testimonial copies (3 copies)
- Smart file mapping based on naming conventions
- Files located in parent directory structure
- Error handling with fallback mechanisms
- Toast notifications for download status

### 🧭 Navigation
- React Router-based navigation
- Back button functionality
- URL-based routing for direct access

## Demo Credentials

- **Username:** admin
- **Password:** 1234

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd job-application-system
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

## Project Structure

```
src/
├── components/
│   ├── Login.jsx          # Login page component
│   ├── Login.css          # Login page styles
│   ├── Dashboard.jsx      # Main dashboard component
│   └── Dashboard.css      # Dashboard styles
├── contexts/
│   └── AuthContext.jsx    # Authentication context
├── data/
│   └── applicantsData.js  # Sample applicant data
├── utils/
│   └── fileDownload.js    # File download utilities
├── App.jsx                # Main app component
├── App.css                # Global app styles
├── index.css              # Global CSS reset and base styles
└── main.jsx               # App entry point
```

## Sample Data

The application includes 3 sample applicants with:
- **6-digit UIDs:** 123456, 234567, 345678
- **Job Titles:** CLOUD INFRASTRUCTURE ENGINEER, SOFTWARE DEVELOPER, DATA ANALYST
- **Applicant Information:** All named John Doe with consistent contact details
- **Education Levels:** BA/BSc/HND, Msc/BSc/HND, Diploma
- **8 Standardized Attachments** per applicant with proper file mapping

## File Structure

```
../sample-application-files/
├── CV.pdf                    # CV attachments
├── CV.docx                   # Alternative CV format
├── Cover_Letter.pdf          # Cover letter attachments
├── cert_copy1.pdf           # Certificate copy 1
├── cert_copy2.jpg           # Certificate copy 2
├── cert_copy3.pdf           # Certificate copy 3
├── testimonial_1.pdf        # Testimonial copy 1
├── testimonial_2.pdf        # Testimonial copy 2
└── testimonial_3.pdf        # Testimonial copy 3
```

## Technologies Used

- **React 18** - UI library with hooks
- **React Router DOM** - Client-side routing
- **Vite** - Build tool and development server
- **CSS3** - Modern styling with gradients and animations
- **JavaScript ES6+** - Modern JavaScript features

## Features in Detail

### Authentication System
- Simple but secure login mechanism
- Persistent sessions across browser refreshes
- Automatic logout functionality

### Dashboard Features
- **Search:** Real-time search across name, email, and position
- **Filtering:** Filter by status and position
- **Sorting:** Click column headers to sort data
- **Statistics:** Overview cards showing key metrics
- **File Downloads:** One-click download for applicant files

### Responsive Design
- Breakpoints for mobile (480px), tablet (768px), and desktop
- Flexible grid layouts
- Touch-friendly interface elements
- Optimized typography and spacing

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is for demonstration purposes.
