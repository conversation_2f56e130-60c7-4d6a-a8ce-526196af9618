# File Integration System Guide

## Overview

The Job Application System now includes a comprehensive file integration system that automatically scans the `downloads` directory and integrates supported files as attachments for applicants. This system provides proper file validation, MIME type detection, and graceful error handling.

## Features

### ✅ Supported File Types
- **PDF** (.pdf) - Max size: 10MB
- **DOCX** (.docx) - Max size: 5MB  
- **JPEG** (.jpg, .jpeg) - Max size: 5MB
- **PNG** (.png) - Max size: 5MB

### 🔍 File Validation
- Extension validation
- File size limits
- MIME type detection
- Graceful error handling for unsupported files

### 🏷️ Smart File Mapping
Files are automatically categorized based on naming conventions:

- **CV/Resume**: Files containing "cv", "resume", or "curriculum"
- **Cover Letter**: Files containing "cover", "letter", or "motivation"
- **Certificate**: Files containing "cert", "certificate", "diploma", or "degree"
- **Testimonial**: Files containing "testimonial", "reference", or "recommendation"

### 📁 Directory Structure
```
job-application-system/
├── downloads/           # Primary file location
│   ├── CV1.docx
│   ├── CV2.pdf
│   ├── cert1.jpg
│   ├── testimonial1.docx
│   └── ...
├── src/
│   ├── utils/
│   │   ├── fileManager.js      # Core file management utilities
│   │   └── fileDownload.js     # Enhanced download utilities
│   ├── services/
│   │   └── fileIntegrationService.js  # Integration service
│   └── data/
│       └── applicantsData.js   # Enhanced with async file loading
```

## How It Works

### 1. File Scanning
The system scans the `downloads` directory for supported files:

```javascript
import { scanDownloadsDirectory } from './src/utils/fileManager.js';

const files = await scanDownloadsDirectory();
```

### 2. File Validation
Each file is validated for:
- Supported file extension
- File size within limits
- Proper MIME type

```javascript
import { validateFile } from './src/utils/fileManager.js';

const validation = validateFile(fileName, fileSize);
if (validation.isValid) {
  // File is valid
} else {
  console.warn(validation.error);
}
```

### 3. Attachment Type Detection
Files are automatically categorized:

```javascript
import { determineAttachmentType } from './src/utils/fileManager.js';

const type = determineAttachmentType('CV1.docx'); // Returns 'cv'
```

### 4. Integration with Applicant Data
The system automatically updates applicant data with real files:

```javascript
import { getApplicantsData } from './src/data/applicantsData.js';

const applicants = await getApplicantsData(); // Returns enhanced data
```

## File Download Priority

The download system checks files in the following order:

1. **Primary**: `./downloads/` directory
2. **Secondary**: `../job-application-system/` directory  
3. **Fallback**: `../sample-application-files/` directory
4. **Last Resort**: `/sample-files/` directory
5. **Default**: Sample file if none found

## Error Handling

### File Not Found
- Graceful fallback to sample files
- User notification with appropriate message
- Console warnings for debugging

### Invalid Files
- Files that don't meet validation criteria are skipped
- Detailed error messages in console
- System continues with valid files

### Network Errors
- Retry mechanisms for file access
- Fallback to cached data when possible
- User-friendly error notifications

## Usage Examples

### Basic Integration
```javascript
// Get enhanced applicant data with real files
const applicants = await getApplicantsData();

// Each applicant now has real file attachments
applicants[0].attachments.forEach(attachment => {
  console.log(`${attachment.name} - ${attachment.type} - ${attachment.size}`);
});
```

### Manual File Operations
```javascript
// Check if a specific file exists
const exists = await checkFileExists('CV1.docx');

// Get detailed file information
const fileInfo = await getFileInfo('CV1.docx');

// Download a file
await downloadFile('CV1.docx', 'John_Doe');
```

### Custom File Integration
```javascript
// Integrate files manually
const result = await integrateFilesIntoApplicantData(originalData);

if (result.success) {
  console.log('Integration successful:', result.stats);
} else {
  console.error('Integration failed:', result.error);
}
```

## Configuration

### File Size Limits
Modify limits in `src/utils/fileManager.js`:

```javascript
export const SUPPORTED_FILE_TYPES = {
  pdf: {
    mimeType: 'application/pdf',
    extensions: ['.pdf'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  // ... other types
};
```

### File Type Patterns
Add new patterns in `src/utils/fileManager.js`:

```javascript
export const FILE_TYPE_MAPPING = {
  cv: {
    patterns: [/^cv/i, /^resume/i, /curriculum/i],
    label: 'Attach your CV',
    priority: 1
  },
  // ... other mappings
};
```

## Testing

Run the test script to verify integration:

```bash
node test-file-integration.js
```

This will test:
- Directory scanning
- File validation
- Type determination
- Attachment creation
- Full integration process

## Troubleshooting

### Files Not Appearing
1. Check file extensions are supported
2. Verify file sizes are within limits
3. Ensure files are in the `downloads` directory
4. Check browser console for error messages

### Download Issues
1. Verify file paths are correct
2. Check server is serving static files
3. Ensure proper MIME types are set
4. Check network connectivity

### Performance Issues
1. Large files may take time to process
2. Consider implementing file caching
3. Monitor memory usage with many files
4. Use pagination for large file lists

## Future Enhancements

- [ ] Backend file upload API
- [ ] File preview functionality
- [ ] Batch file operations
- [ ] File compression/optimization
- [ ] Advanced file search and filtering
- [ ] File versioning system
- [ ] Automated file backup
